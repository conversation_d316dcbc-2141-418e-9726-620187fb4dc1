using Microsoft.EntityFrameworkCore;
using YendorCats.API.Models;

namespace YendorCats.API.Data
{
    /// <summary>
    /// Database context for the Yendor Cats application
    /// </summary>
    public class AppDbContext : DbContext
    {
        /// <summary>
        /// Constructor for the database context
        /// </summary>
        /// <param name="options">The DbContext options</param>
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        /// <summary>
        /// Collection of cats in the database
        /// </summary>
        public DbSet<Cat> Cats { get; set; } = null!;
        
        /// <summary>
        /// Collection of cat images in the database
        /// </summary>
        public DbSet<CatImage> CatImages { get; set; } = null!;

        /// <summary>
        /// Collection of cat gallery images with metadata
        /// </summary>
        public DbSet<CatGalleryImage> CatGalleryImages { get; set; } = null!;

        /// <summary>
        /// Collection of cat profiles for breeding program management
        /// </summary>
        public DbSet<CatProfile> CatProfiles { get; set; } = null!;

        /// <summary>
        /// Collection of B2 synchronization logs for audit trails
        /// </summary>
        public DbSet<B2SyncLog> B2SyncLogs { get; set; } = null!;

        /// <summary>
        /// Collection of users in the database
        /// </summary>
        public DbSet<User> Users { get; set; } = null!;

        /// <summary>
        /// Collection of admin users in the database
        /// </summary>
        public DbSet<AdminUser> AdminUsers { get; set; } = null!;

        /// <summary>
        /// Collection of clients in the database
        /// </summary>
        public DbSet<Client> Clients { get; set; } = null!;

        /// <summary>
        /// Collection of appointments in the database
        /// </summary>
        public DbSet<Appointment> Appointments { get; set; } = null!;

        /// <summary>
        /// Configure the database model
        /// </summary>
        /// <param name="modelBuilder">The model builder</param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Cat entity
            modelBuilder.Entity<Cat>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Breed).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Gender).IsRequired().HasMaxLength(1);
                entity.Property(e => e.Color).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Markings).HasMaxLength(200);
                
                // Configure relationships
                entity.HasOne(e => e.Mother)
                    .WithMany()
                    .HasForeignKey(e => e.MotherId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired(false);
                
                entity.HasOne(e => e.Father)
                    .WithMany()
                    .HasForeignKey(e => e.FatherId)
                    .OnDelete(DeleteBehavior.Restrict)
                    .IsRequired(false);
            });

            // Configure CatImage entity
            modelBuilder.Entity<CatImage>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.FilePath).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.ContentType).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Caption).HasMaxLength(500);

                // Configure relationships
                entity.HasOne(e => e.Cat)
                    .WithMany(c => c.Images)
                    .HasForeignKey(e => e.CatId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure CatGalleryImage entity with dual storage support
            modelBuilder.Entity<CatGalleryImage>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                // Core properties
                entity.Property(e => e.Filename).IsRequired().HasMaxLength(255);
                entity.Property(e => e.CatId).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Title).HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Alt).HasMaxLength(500);
                entity.Property(e => e.Tags).HasMaxLength(1000);
                
                // File properties
                entity.Property(e => e.FileSize).IsRequired();
                entity.Property(e => e.MimeType).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Width).IsRequired();
                entity.Property(e => e.Height).IsRequired();
                entity.Property(e => e.Format).IsRequired().HasMaxLength(20);
                
                // Dual storage properties
                entity.Property(e => e.StorageProvider).IsRequired().HasMaxLength(20);
                entity.Property(e => e.S3Key).HasMaxLength(500);
                entity.Property(e => e.S3Bucket).HasMaxLength(100);
                entity.Property(e => e.S3Url).HasMaxLength(1000);
                entity.Property(e => e.B2Key).HasMaxLength(500);
                entity.Property(e => e.B2Bucket).HasMaxLength(100);
                entity.Property(e => e.B2Url).HasMaxLength(1000);
                
                // Metadata properties
                entity.Property(e => e.ExifData).HasMaxLength(2000);
                entity.Property(e => e.IsFeatured).IsRequired().HasDefaultValue(false);
                entity.Property(e => e.IsActive).IsRequired().HasDefaultValue(true);
                entity.Property(e => e.IsPublic).IsRequired().HasDefaultValue(true);
                entity.Property(e => e.DisplayOrder).IsRequired().HasDefaultValue(0);
                
                // Analytics properties
                entity.Property(e => e.ViewCount).IsRequired().HasDefaultValue(0);
                entity.Property(e => e.LikeCount).IsRequired().HasDefaultValue(0);
                entity.Property(e => e.DownloadCount).IsRequired().HasDefaultValue(0);
                
                // Timestamps
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();
                entity.Property(e => e.LastViewedAt);
                
                // Performance indexes for dual storage
                entity.HasIndex(e => e.Filename).IsUnique();
                entity.HasIndex(e => e.CatId);
                entity.HasIndex(e => e.StorageProvider);
                entity.HasIndex(e => e.S3Key);
                entity.HasIndex(e => e.B2Key);
                entity.HasIndex(e => e.IsActive);
                entity.HasIndex(e => e.IsPublic);
                entity.HasIndex(e => e.IsFeatured);
                entity.HasIndex(e => e.DisplayOrder);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.ViewCount);
                entity.HasIndex(e => new { e.CatId, e.IsActive, e.IsPublic });
                entity.HasIndex(e => new { e.StorageProvider, e.IsActive });
                entity.HasIndex(e => new { e.IsFeatured, e.DisplayOrder });

                // Configure relationship with CatProfile
                entity.HasOne(e => e.CatProfile)
                    .WithMany(cp => cp.GalleryImages)
                    .HasForeignKey(e => e.CatId)
                    .HasPrincipalKey(cp => cp.CatId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .IsRequired(false);
            });

            // Configure CatProfile entity for breeding program management
            modelBuilder.Entity<CatProfile>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                // Core properties
                entity.Property(e => e.CatId).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CatName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.RegisteredName).HasMaxLength(200);
                entity.Property(e => e.Breed).HasMaxLength(100);
                entity.Property(e => e.Bloodline).HasMaxLength(200);
                entity.Property(e => e.Gender).IsRequired().HasMaxLength(1);
                entity.Property(e => e.Color).HasMaxLength(100);
                entity.Property(e => e.Pattern).HasMaxLength(100);
                entity.Property(e => e.EyeColor).HasMaxLength(50);
                
                // Pedigree properties
                entity.Property(e => e.SireId).HasMaxLength(50);
                entity.Property(e => e.SireName).HasMaxLength(100);
                entity.Property(e => e.DamId).HasMaxLength(50);
                entity.Property(e => e.DamName).HasMaxLength(100);
                
                // Registration properties
                entity.Property(e => e.RegistrationNumber).HasMaxLength(100);
                entity.Property(e => e.RegistrationBody).HasMaxLength(100);
                entity.Property(e => e.Microchip).HasMaxLength(50);
                entity.Property(e => e.Tattoo).HasMaxLength(50);
                
                // Breeding properties
                entity.Property(e => e.BreedingStatus).HasMaxLength(50);
                entity.Property(e => e.AvailabilityStatus).HasMaxLength(50);
                entity.Property(e => e.Titles).HasMaxLength(500);
                entity.Property(e => e.Awards).HasMaxLength(1000);
                
                // Health properties
                entity.Property(e => e.HealthStatus).HasMaxLength(50);
                entity.Property(e => e.HealthNotes).HasMaxLength(1000);
                entity.Property(e => e.VetInfo).HasMaxLength(500);
                
                // Location properties
                entity.Property(e => e.CurrentLocation).HasMaxLength(200);
                entity.Property(e => e.OwnerInfo).HasMaxLength(500);
                
                // Display properties
                entity.Property(e => e.ProfileImage).HasMaxLength(500);
                entity.Property(e => e.Description).HasMaxLength(2000);
                entity.Property(e => e.Personality).HasMaxLength(1000);
                entity.Property(e => e.Notes).HasMaxLength(2000);
                entity.Property(e => e.Tags).HasMaxLength(1000);
                
                // Status properties
                entity.Property(e => e.IsActive).IsRequired().HasDefaultValue(true);
                entity.Property(e => e.IsPublic).IsRequired().HasDefaultValue(true);
                entity.Property(e => e.IsFeatured).IsRequired().HasDefaultValue(false);
                entity.Property(e => e.DisplayOrder).IsRequired().HasDefaultValue(0);
                
                // Timestamps
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.UpdatedAt).IsRequired();
                entity.Property(e => e.LastHealthCheck);
                
                // Performance indexes for breeding program
                entity.HasIndex(e => e.CatId).IsUnique();
                entity.HasIndex(e => e.CatName);
                entity.HasIndex(e => e.Breed);
                entity.HasIndex(e => e.Bloodline);
                entity.HasIndex(e => e.Gender);
                entity.HasIndex(e => e.BreedingStatus);
                entity.HasIndex(e => e.AvailabilityStatus);
                entity.HasIndex(e => e.SireId);
                entity.HasIndex(e => e.DamId);
                entity.HasIndex(e => e.BirthDate);
                entity.HasIndex(e => e.IsActive);
                entity.HasIndex(e => e.IsPublic);
                entity.HasIndex(e => e.IsFeatured);
                entity.HasIndex(e => e.DisplayOrder);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => new { e.Gender, e.BreedingStatus, e.IsActive });
                entity.HasIndex(e => new { e.Breed, e.IsActive, e.IsPublic });
                entity.HasIndex(e => new { e.IsFeatured, e.DisplayOrder });
                entity.HasIndex(e => new { e.AvailabilityStatus, e.Gender, e.IsActive });
                entity.HasIndex(e => new { e.SireId, e.DamId });
            });

            // Configure B2SyncLog entity for audit trails
            modelBuilder.Entity<B2SyncLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                // Core properties
                entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Operation).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(50);
                entity.Property(e => e.S3Key).HasMaxLength(500);
                entity.Property(e => e.S3Bucket).HasMaxLength(100);
                entity.Property(e => e.B2Key).HasMaxLength(500);
                entity.Property(e => e.B2Bucket).HasMaxLength(100);
                entity.Property(e => e.B2FileId).HasMaxLength(100);
                
                // Metadata properties
                entity.Property(e => e.FileSize);
                entity.Property(e => e.ContentType).HasMaxLength(100);
                entity.Property(e => e.Checksum).HasMaxLength(64);
                
                // Process properties
                entity.Property(e => e.ProcessingTimeMs);
                entity.Property(e => e.RetryCount).IsRequired().HasDefaultValue(0);
                entity.Property(e => e.ErrorMessage).HasMaxLength(2000);
                entity.Property(e => e.ErrorDetails).HasMaxLength(4000);
                
                // Context properties
                entity.Property(e => e.BatchId).HasMaxLength(100);
                entity.Property(e => e.UserId).HasMaxLength(100);
                entity.Property(e => e.SessionId).HasMaxLength(100);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                
                // Timestamps
                entity.Property(e => e.CreatedAt).IsRequired();
                entity.Property(e => e.CompletedAt);
                entity.Property(e => e.NextRetryAt);
                
                // Performance indexes for audit trails
                entity.HasIndex(e => e.FileName);
                entity.HasIndex(e => e.Operation);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.S3Key);
                entity.HasIndex(e => e.B2Key);
                entity.HasIndex(e => e.B2FileId);
                entity.HasIndex(e => e.BatchId);
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.CompletedAt);
                entity.HasIndex(e => new { e.Operation, e.Status });
                entity.HasIndex(e => new { e.Status, e.CreatedAt });
                entity.HasIndex(e => new { e.BatchId, e.Status });
                entity.HasIndex(e => new { e.Operation, e.Status, e.CreatedAt });
            });

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
                entity.Property(e => e.FirstName).HasMaxLength(50);
                entity.Property(e => e.LastName).HasMaxLength(50);
                entity.Property(e => e.PasswordHash).IsRequired();
                entity.Property(e => e.PasswordSalt).IsRequired();
                entity.Property(e => e.Role).IsRequired().HasMaxLength(20);
                
                // Create unique index for username
                entity.HasIndex(e => e.Username).IsUnique();
                
                // Create unique index for email
                entity.HasIndex(e => e.Email).IsUnique();
            });

            // Configure AdminUser entity
            modelBuilder.Entity<AdminUser>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PasswordHash).IsRequired();
                entity.Property(e => e.PasswordSalt).IsRequired();
                entity.Property(e => e.Role).IsRequired().HasMaxLength(20);

                // Create unique indexes
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
            });

            // Configure Client entity
            modelBuilder.Entity<Client>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(50);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Phone).HasMaxLength(20);
                entity.Property(e => e.Address).HasMaxLength(200);
                entity.Property(e => e.City).HasMaxLength(50);
                entity.Property(e => e.State).HasMaxLength(50);
                entity.Property(e => e.PostalCode).HasMaxLength(20);
                entity.Property(e => e.Country).HasMaxLength(50);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                
                // Create index for email
                entity.HasIndex(e => e.Email);
                
                // Create index for name searching
                entity.HasIndex(e => e.LastName);
                entity.HasIndex(e => e.FirstName);
            });

            // Configure Appointment entity
            modelBuilder.Entity<Appointment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Location).HasMaxLength(200);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Notes).HasMaxLength(1000);
                
                // Configure relationships
                entity.HasOne(e => e.Client)
                    .WithMany(c => c.Appointments)
                    .HasForeignKey(e => e.ClientId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.Cat)
                    .WithMany()
                    .HasForeignKey(e => e.CatId)
                    .OnDelete(DeleteBehavior.SetNull)
                    .IsRequired(false);
                
                // Create indexes for querying
                entity.HasIndex(e => e.StartTime);
                entity.HasIndex(e => e.ClientId);
                entity.HasIndex(e => e.Status);
            });
        }
    }
}
