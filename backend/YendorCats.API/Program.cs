using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using System.Text;
using YendorCats.API.Configuration;
using YendorCats.API.Data;
using YendorCats.API.Middleware;
using YendorCats.API.Services;
using System.Reflection;
using Amazon.S3;
using System.Text.Json;
using Pomelo.EntityFrameworkCore.MySql.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();

// Re-enable Secrets Manager Service for proper authentication
Log.Information("Secrets Manager Service enabled for production security");
builder.Services.AddSingleton<ISecretsManagerService, SecretsManagerService>();

// Configure S3 client using environment variables (no Vault dependency)
Log.Information("Configuring S3 services with environment variables");
builder.Services.AddSingleton<IAmazonS3>(sp =>
{
    var s3Config = new AmazonS3Config
    {
        ServiceURL = builder.Configuration["AWS:S3:ServiceUrl"],
        ForcePathStyle = true,
    };

    string s3AccessKey = null;
    string s3SecretKey = null;

    // Primary: Get credentials from environment variables directly
    s3AccessKey = Environment.GetEnvironmentVariable("AWS_S3_ACCESS_KEY");
    s3SecretKey = Environment.GetEnvironmentVariable("AWS_S3_SECRET_KEY");

    // Log credential loading status for debugging
    Log.Information("S3 AccessKey from env AWS_S3_ACCESS_KEY: {HasKey}, Length: {Length}",
        !string.IsNullOrEmpty(s3AccessKey), s3AccessKey?.Length ?? 0);
    Log.Information("S3 SecretKey from env AWS_S3_SECRET_KEY: {HasKey}, Length: {Length}",
        !string.IsNullOrEmpty(s3SecretKey), s3SecretKey?.Length ?? 0);

    if (!string.IsNullOrEmpty(s3AccessKey) && !string.IsNullOrEmpty(s3SecretKey))
    {
        Log.Information("Using S3 credentials from environment variables AWS_S3_ACCESS_KEY/AWS_S3_SECRET_KEY");
        return new AmazonS3Client(s3AccessKey, s3SecretKey, s3Config);
    }

    // Fallback: Try appsettings configuration (for development)
    s3AccessKey = builder.Configuration["AWS:S3:AccessKey"];
    s3SecretKey = builder.Configuration["AWS:S3:SecretKey"];

    // Log what we got from configuration
    Log.Information("S3 AccessKey from config: {HasKey}, Length: {Length}, Value: {Value}",
        !string.IsNullOrEmpty(s3AccessKey), s3AccessKey?.Length ?? 0, s3AccessKey);
    Log.Information("S3 SecretKey from config: {HasKey}, Length: {Length}",
        !string.IsNullOrEmpty(s3SecretKey), s3SecretKey?.Length ?? 0);

    if (!string.IsNullOrEmpty(s3AccessKey) && !string.IsNullOrEmpty(s3SecretKey) &&
        !s3AccessKey.StartsWith("${") && !s3SecretKey.StartsWith("${"))
    {
        Log.Information("Using S3 credentials from configuration (appsettings)");
        return new AmazonS3Client(s3AccessKey, s3SecretKey, s3Config);
    }

    // Fallback: Standard AWS environment variables
    s3AccessKey = Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID");
    s3SecretKey = Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY");

    if (!string.IsNullOrEmpty(s3AccessKey) && !string.IsNullOrEmpty(s3SecretKey))
    {
        Log.Information("Using S3 credentials from direct environment variables");
        return new AmazonS3Client(s3AccessKey, s3SecretKey, s3Config);
    }

    // Final fallback - create client without credentials (will likely fail but prevents startup crash)
    Log.Warning("No S3 credentials found. Creating S3 client without credentials - S3 operations will likely fail.");
    return new AmazonS3Client(s3Config);
});

// Add S3 storage service
builder.Services.AddScoped<IS3StorageService, S3StorageService>();

// Configure JWT settings
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JwtSettings"));

// Configure storage provider settings
builder.Services.Configure<YendorCats.API.Configuration.StorageProviderConfiguration>(
    builder.Configuration.GetSection("StorageProviders"));

// Configure Entity Framework with SQLite
builder.Services.AddDbContext<AppDbContext>(options =>
{
    var environment = builder.Environment;
    var configuration = builder.Configuration;

    // Get connection string from configuration or use default
    string sqliteConnectionString;

    if (environment.IsDevelopment())
    {
        // Use local path for development
        sqliteConnectionString = configuration.GetConnectionString("SqliteConnection")
            ?? "Data Source=Data/yendorcats.db";
        Log.Information("Using SQLite database for development with persistence: {ConnectionString}", sqliteConnectionString);
    }
    else
    {
        // Use container path for production/staging
        sqliteConnectionString = configuration.GetConnectionString("SqliteConnection")
            ?? "Data Source=/app/data/yendorcats.db";
        Log.Information("Using SQLite database for production with persistence: {ConnectionString}", sqliteConnectionString);

        // Ensure the database directory exists
        var dbPath = sqliteConnectionString.Replace("Data Source=", "");
        var dbDir = Path.GetDirectoryName(dbPath);

        if (!string.IsNullOrEmpty(dbDir) && !Directory.Exists(dbDir))
        {
            try
            {
                Directory.CreateDirectory(dbDir);
                Log.Information("Created database directory: {DbDir}", dbDir);
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Failed to create database directory: {DbDir}", dbDir);
            }
        }
    }

    // Configure SQLite with connection string
    options.UseSqlite(
        sqliteConnectionString,
        sqliteOptions =>
        {
            sqliteOptions.MigrationsAssembly(typeof(AppDbContext).Assembly.FullName);
            // Enable connection resilience
            sqliteOptions.CommandTimeout(30);
        });

    // Commented out MySQL connection for future migration if needed
    /*
    if (environment.IsDevelopment())
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException(
                "Development connection string not found. Please check your appsettings.Development.json file.");
        }
        Log.Information("Using MySQL connection string: {ConnectionString}", connectionString);
        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), 
            mySqlOptions => mySqlOptions.EnableRetryOnFailure());
    }
    */
});

// Configure Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Yendor Cats API",
        Version = "v1",
        Description = "API for the Yendor Cats exotic cat breeder website.",
        Contact = new OpenApiContact
        {
            Name = "Admin",
            Email = "<EMAIL>"
        }
    });

    // Include XML comments in Swagger
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    c.IncludeXmlComments(xmlPath);

    // Add JWT authentication to Swagger UI
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigins",
        policy =>
        {
            policy.WithOrigins(
                "http://localhost:5000",
                "http://localhost:5003",
                "https://localhost:5001",
                "https://yendorcats.com",
                "https://www.yendorcats.com")
                .WithMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .WithHeaders("Content-Type", "Authorization", "X-Requested-With")
                .AllowCredentials()
                .SetPreflightMaxAge(TimeSpan.FromMinutes(10));
        });
});

// Configure JWT authentication
builder.Services.AddAuthentication(x =>
{
    x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    // Use appsettings for JWT configuration to avoid blocking calls during startup
    var jwtSettings = builder.Configuration.GetSection("JwtSettings");
    var secretKey = jwtSettings["Secret"];

    if (string.IsNullOrEmpty(secretKey))
    {
        throw new InvalidOperationException("JWT Secret is required. Set YENDOR_JWT_SECRET environment variable.");
    }

    if (secretKey.Length < 32)
    {
        throw new InvalidOperationException("JWT Secret must be at least 32 characters long.");
    }

    Log.Information("Configuring JWT authentication with appsettings values");
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secretKey)),
        ValidateIssuer = !string.IsNullOrEmpty(jwtSettings["Issuer"]),
        ValidIssuer = jwtSettings["Issuer"],
        ValidateAudience = !string.IsNullOrEmpty(jwtSettings["Audience"]),
        ValidAudience = jwtSettings["Audience"],
        ClockSkew = TimeSpan.Zero
    };
});

// Register services
builder.Services.AddScoped<ICatService, CatService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IImageService, ImageService>();
builder.Services.AddScoped<IImageMetadataService, ImageMetadataService>();
builder.Services.AddScoped<IAdminAuthService, AdminAuthService>();
builder.Services.AddScoped<IPhotoIndexService, PhotoIndexService>();
builder.Services.AddScoped<ICatMetadataService, CatMetadataService>();
builder.Services.AddScoped<IClientService, ClientService>();
builder.Services.AddScoped<IAppointmentService, AppointmentService>();

// Register new hybrid storage infrastructure
builder.Services.AddScoped<YendorCats.API.Data.Repositories.IGalleryRepository, YendorCats.API.Data.Repositories.GalleryRepository>();
builder.Services.AddScoped<YendorCats.API.Data.Repositories.ICatProfileRepository, YendorCats.API.Data.Repositories.CatProfileRepository>();

// Register B2 sync services
builder.Services.AddScoped<YendorCats.API.Services.B2Sync.IB2SyncService, YendorCats.API.Services.B2Sync.B2SyncService>();

// Register migration services
builder.Services.AddScoped<YendorCats.API.Services.Migration.IS3ToDbMigrationService, YendorCats.API.Services.Migration.S3ToDbMigrationService>();
builder.Services.AddScoped<YendorCats.API.Services.Migration.MigrationValidator>();
builder.Services.AddScoped<YendorCats.API.Services.Migration.MigrationReporter>();

// Register compatibility services
builder.Services.AddScoped<YendorCats.API.Services.Compatibility.IS3CompatibilityService, YendorCats.API.Services.Compatibility.S3CompatibilityService>();

// Register gallery services
builder.Services.AddScoped<YendorCats.API.Services.Gallery.IGalleryService, YendorCats.API.Services.Gallery.GalleryService>();
builder.Services.AddScoped<YendorCats.API.Services.Gallery.IGalleryCacheService, YendorCats.API.Services.Gallery.GalleryCacheService>();

// Register performance services
builder.Services.AddScoped<YendorCats.API.Services.Performance.ICacheWarmupService, YendorCats.API.Services.Performance.CacheWarmupService>();
builder.Services.AddScoped<YendorCats.API.Services.Performance.IPerformanceMetricsService, YendorCats.API.Services.Performance.PerformanceMetricsService>();
builder.Services.AddScoped<YendorCats.API.Services.Performance.IThumbnailService, YendorCats.API.Services.Performance.ThumbnailService>();

// Add memory cache for rate limiting middleware
builder.Services.AddMemoryCache();

// Add distributed cache for multi-tier caching strategy
// Using in-memory distributed cache for development/single instance
// In production, consider Redis or SQL Server distributed cache
builder.Services.AddDistributedMemoryCache();

var app = builder.Build();

// Configure middleware pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseDeveloperExceptionPage();
}
else
{
    // Custom exception handling for production
    app.UseMiddleware<ErrorHandlingMiddleware>();
    app.UseHsts();
}

// Add security headers middleware
app.UseMiddleware<SecurityHeadersMiddleware>();

// Add rate limiting middleware
app.UseMiddleware<RateLimitingMiddleware>();

// Log S3 configuration for debugging
var bucketName = builder.Configuration["AWS:S3:BucketName"];
Log.Information("S3 Bucket configured: {BucketName}", bucketName ?? "NOT CONFIGURED");

// Configure S3 CORS on startup for proper API access
if (!string.IsNullOrEmpty(bucketName))
{
    try
    {
        using (var scope = app.Services.CreateScope())
        {
            var s3Service = scope.ServiceProvider.GetRequiredService<IS3StorageService>();
            s3Service.ConfigureCorsAsync().GetAwaiter().GetResult();
            Log.Information("S3 CORS configuration applied successfully");
        }
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Failed to configure S3 CORS on startup - continuing without CORS");
        // Don't fail startup if CORS configuration fails
    }
}

// Apply migrations automatically in development
if (app.Environment.IsDevelopment())
{
    try
    {
        using (var scope = app.Services.CreateScope())
        {
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            Log.Information("Starting database migration...");
            // Only run migrations if not using in-memory database
            if (!dbContext.Database.IsInMemory())
            {
                dbContext.Database.Migrate();
                Log.Information("Database migration completed successfully");
            }
            else
            {
                // For in-memory database, ensure it's created
                dbContext.Database.EnsureCreated();
                Log.Information("In-memory database created successfully");
            }

            // Initialize default admin user if none exist
            Log.Information("Initializing default admin user...");
            var adminAuthService = scope.ServiceProvider.GetRequiredService<IAdminAuthService>();
            await adminAuthService.InitializeDefaultAdminAsync();
            Log.Information("Admin user initialization completed");
        }
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Failed to initialize database or admin user");
        // Continue startup even if database initialization fails
    }
}

// Only use HTTPS redirection in production
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
}

// Configure static files with default file support
app.UseDefaultFiles(new DefaultFilesOptions
{
    DefaultFileNames = new List<string> { "index.html" }
});
app.UseFileServer();

// Add SPA fallback route handler
app.MapFallbackToFile("index.html");

// Enable CORS
app.UseCors("AllowSpecificOrigins");

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();

// Password change required middleware
app.UsePasswordChangeRequired();

// Configure file watcher in development mode for real-time updates
if (app.Environment.IsDevelopment())
{
    app.UseLiveReload();
    // Temporarily disable file watcher for debugging
    // app.UseFileWatcher(app.Environment, app.Services.GetRequiredService<ILogger<FileSystemWatcher>>());
    Log.Information("File watcher temporarily disabled for debugging");
}

app.MapControllers();

try
{
    Log.Information("Starting Yendor Cats API");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application start-up failed");
}
finally
{
    Log.CloseAndFlush();
}
